package com.jt.system.infra.service;

import com.jt.framework.mybatis.service.BaseService;
import com.jt.system.infra.entity.SysRoleDashboardEntity;

/**
 * 角色与看板关系Service接口
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface ISysRoleDashboardService extends BaseService<SysRoleDashboardEntity> {

    /**
     * 根据角色ID删除相关数据的方法。
     *
     * @param roleId 要删除的角色ID，类型为Long。
     */
    void deleteByRoleId(Long roleId);
}
