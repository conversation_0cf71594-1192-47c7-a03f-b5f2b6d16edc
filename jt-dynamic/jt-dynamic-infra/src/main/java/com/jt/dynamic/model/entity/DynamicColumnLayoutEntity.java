package com.jt.dynamic.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jt.framework.mybatis.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户动态列布局
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@TableName("dynamic_column_layout")
@Schema(description = "用户动态列布局")
@Data
public class DynamicColumnLayoutEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "列表Id")
    private Long gridId;

    @Schema(description = "列表编码")
    private String gridCode;

    @Schema(description = "布局编码")
    private String code;

    @Schema(description = "布局名称")
    private String name;

    @Schema(description = "状态：0-启用  1-不启用")
    private Integer status;

}
