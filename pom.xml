<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.jt</groupId>
    <artifactId>jt-cloud</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>jt-cloud</name>
    <description>jt-platform</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.1</version>
    </parent>

    <modules>
	    <module>jt-framework</module>
		<module>jt-file</module>
		<module>jt-system</module>
        <module>jt-gateway</module>
        <module>jt-parent</module>
        <module>jt-tool</module>
        <module>jt-message</module>
        <module>jt-inf</module>
        <module>jt-export</module>
        <module>jt-dynamic</module>
    </modules>
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <jtVersion>1.0.0</jtVersion>
        <skipTests>true</skipTests>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <spring.cloud.version>2023.0.2</spring.cloud.version>
        <spring.cloud.alibaba.version>2023.0.1.0</spring.cloud.alibaba.version>
        <mybatisplus.version>3.5.7</mybatisplus.version>
        <dynamic-datasource.version>4.1.3</dynamic-datasource.version>
        <redisson.version>3.21.3</redisson.version>
        <lock4j.version>2.2.7</lock4j.version>
        <knife4j.version>4.5.0</knife4j.version>
        <hutool.version>5.8.28</hutool.version>
        <guava.version>33.0.0-jre</guava.version>
        <captcha.version>1.6.2</captcha.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <minio.version>8.5.17</minio.version>
        <mybatis.spring.version>3.0.3</mybatis.spring.version>
        <bouncycastle.version>1.69</bouncycastle.version>
        <transmittable.thread.local.version>2.14.2</transmittable.thread.local.version>
        <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
        <easytrans.version>3.0.0</easytrans.version>
        <jsoup.version>1.16.1</jsoup.version>
        <ip2region.version>2.7.0</ip2region.version>
        <aliyun.oss.version>3.15.2</aliyun.oss.version>
        <fastdfs.version>1.27.2</fastdfs.version>
        <shardingsphere.version>5.5.2</shardingsphere.version>
        <mysql.version>8.0.33</mysql.version>
        <velocity.version>2.3</velocity.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-spring-boot-starter</artifactId>
                <version>${easytrans.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-mybatis-plus-extend</artifactId>
                <version>${easytrans.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>${captcha.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.oss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${fastdfs.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc</artifactId>
                <version>${shardingsphere.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!-- 开发环境 -->
                <profile.name>dev</profile.name>
                <nacos.server-addr>127.0.0.1:8848</nacos.server-addr>
                <nacos.namespace></nacos.namespace>
                <nacos.username></nacos.username>
                <nacos.password></nacos.password>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <!-- 测试环境，打包命令：mvn clean install -Ptest -->
                <profile.name>test</profile.name>
                <nacos.server-addr>172.17.0.1:8848</nacos.server-addr>
                <nacos.namespace></nacos.namespace>
                <nacos.username></nacos.username>
                <nacos.password></nacos.password>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- 生产环境，打包命令：mvn clean install -Pprod -->
                <profile.name>prod</profile.name>
                <nacos.server-addr>172.17.0.1:8848</nacos.server-addr>
                <nacos.namespace></nacos.namespace>
                <nacos.username></nacos.username>
                <nacos.password></nacos.password>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


    <distributionManagement>
        <!--构件的稳定版部署位置 -->
        <repository>
            <!-- id和settings.xml中server的id保持一致 -->
            <id>releases</id>
            <url>http://218.13.91.108:30005/repository/maven-releases/</url>
        </repository>
        <!-- 构件的快照版部署位置 -->
        <snapshotRepository>
            <!-- id和settings.xml中server的id保持一致 -->
            <id>snapshots</id>
            <url>http://218.13.91.108:30005/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <!-- id和settings.xml中server的id保持一致 -->
            <id>releases</id>
            <url>http://218.13.91.108:30005/repository/maven-releases/</url>
        </repository>
    </repositories>

<!--    <repositories>-->
<!--        <repository>-->
<!--            <id>public</id>-->
<!--            <name>阿里云公共仓库</name>-->
<!--            <url>https://maven.aliyun.com/repository/public/</url>-->
<!--            <releases>-->
<!--                <enabled>true</enabled>-->
<!--            </releases>-->
<!--        </repository>-->
<!--    </repositories>-->
<!--    <pluginRepositories>-->
<!--        <pluginRepository>-->
<!--            <id>public</id>-->
<!--            <name>阿里云公共仓库</name>-->
<!--            <url>https://maven.aliyun.com/repository/public/</url>-->
<!--            <releases>-->
<!--                <enabled>true</enabled>-->
<!--            </releases>-->
<!--            <snapshots>-->
<!--                <enabled>false</enabled>-->
<!--            </snapshots>-->
<!--        </pluginRepository>-->
<!--    </pluginRepositories>-->

</project>
