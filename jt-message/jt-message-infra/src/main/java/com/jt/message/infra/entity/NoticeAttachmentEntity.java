package com.jt.message.infra.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jt.framework.mybatis.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Package com.jt.message.entity
 * <AUTHOR>
 * @date 2025/2/13 19:57
 */

/**
 * 通知附件表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName("notice_attachment")
public class NoticeAttachmentEntity extends BaseEntity {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 关联id
     */
    private Long noticeId;

    /**
     * 文件id
     */
    private String fileId;

}